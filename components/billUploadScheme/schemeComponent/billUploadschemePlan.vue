<script setup lang="ts">
// 执行方案-日程安排
import { message } from 'ant-design-vue';
import { onMounted, onBeforeUnmount, ref, reactive, watch, computed, nextTick, defineProps, defineEmits, defineExpose } from 'vue';

import { errorModal, resolveParam, routerParam } from '@haierbusiness-front/utils';
import {
  DemandSubmitObj,
  schemeStaysArr,
  schemePlacesArr,
  schemeCateringsArr,
  schemeVehiclesArr,
  schemeAttendantsArr,
  schemeActivitiesArr,
  schemeInsurancesArr,
} from '@haierbusiness-front/common-libs';

import schemeStay from './billUploadschemeStay.vue';
import schemePlace from './billUploadschemePlace.vue';
import schemeCatering from './billUploadschemeCaterings.vue';
import schemeVehicle from './billUploadschemeVehicles.vue';
import schemeAttendant from './billUploadschemeAttendant.vue';
import schemeActivity from './billUploadschemeActivity.vue';
import schemeInsurance from './billUploadschemeInsurance.vue';
import { useRoute } from 'vue-router';

const props = defineProps({
  schemeContainerRef: {}, // 父级
  demandInfo: {
    type: Object,
    default: {},
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  hotelList: {
    type: Array,
    default: [],
  },
  merchantType: {
    type: Number,
    default: null,
  },
  processNode: {
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
  isCateringStandardControl: {
    // 是否控制餐标等其他标准配置 - 1:不可修改,2:可以提高,3:可以降低
    type: String,
    default: '',
  },
  readonly: {
    // 是否为只读模式
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['planPriceEmit', 'planEachPriceEmit', 'schemePlanEmit']);
const route = useRoute();
const schemeStays = ref<Array<any>>([]);
const schemeDifferenceStays = ref<Array<any>>([]);
const schemePlaces = ref<Array<any>>([]);
const schemeCaterings = ref<Array<any>>([]);
const schemeVehicles = ref<Array<any>>([]);
const schemeAttendants = ref<Array<any>>([]);
const schemeActivities = ref<Array<any>>([]);
const schemeInsurances = ref<Array<any>>([]);

const stayRef = ref<any>(null);
const placeRef = ref<any>(null);
const cateringRef = ref<any>(null);
const vehicleRef = ref<any>(null);
const attendantRef = ref<any>(null);
const activityRef = ref<any>(null);
const insuranceRef = ref<any>(null);

const schemeInfo = ref<DemandSubmitObj>({}); // 我的方案详情
const planList = ref<Array<any>>([]); // 计划列表
const schemePlanList = ref<Array<any>>([]); // 方案计划列表
const dateRangeList = ref<Array<any>>([]); // 所有日期列表

const stayPriceArr = ref<Array<any>>([]); // 住宿 - 方案金额
const placePriceArr = ref<Array<any>>([]); // 会场 - 方案金额
const cateringPriceArr = ref<Array<any>>([]); // 用餐 - 方案金额
const vehiclePriceArr = ref<Array<any>>([]); // 用车 - 方案金额
const attendantPriceArr = ref<Array<any>>([]); // 服务人员 - 方案金额
const activityPriceArr = ref<Array<any>>([]); // 拓展活动 - 方案金额
const insurancePriceArr = ref<Array<any>>([]); // 保险 - 方案金额
const merchantTypeCurrent = ref(props.merchantType);

// 滚动检测相关
const planComponentRef = ref<HTMLElement | null>(null); // 组件根元素引用
const showDateAffix = ref<boolean>(true); // 是否显示固定日期标题
let intersectionObserver: IntersectionObserver | null = null; // 交叉观察器
watch(
  props,
  (newVal) => {
    merchantTypeCurrent.value = newVal.merchantType;
  },
  { deep: true, immediate: true },
);
watch(
  () => [props.demandInfo, props.schemeCacheInfo, props.readonly],
  async () => {
    // 我的方案详情
    schemeInfo.value = JSON.parse(JSON.stringify(props.demandInfo));
    console.log("缓存返回给住宿的数据",props.demandInfo, props.schemeCacheInfo);
    // 缓存
    const dataInfo2 = props.schemeCacheInfo;

    if (dataInfo2.startDate && dataInfo2.endDate) {
      console.log("到这了！！！！！");
      await changeDate(dataInfo2.startDate, dataInfo2.endDate);
      schemePlanList.value = [];

      // 日程安排反显 - 优先使用 demandInfo 中的数据，如果没有则使用 schemeCacheInfo
      dateRangeList.value.forEach((e, i) => {
        let stays =
          (dataInfo2.stays && dataInfo2.stays.filter((e1) => e === e1.demandDate)) ||
          [];
        let places =
          (dataInfo2.places && dataInfo2.places.filter((e1) => e === e1.demandDate)) ||
          [];
        let caterings =
          (dataInfo2.caterings && dataInfo2.caterings.filter((e1) => e === e1.demandDate)) ||
          [];
        let vehicles =
          (dataInfo2.vehicles && dataInfo2.vehicles.filter((e1) => e === e1.demandDate)) ||
          [];
        let attendants =
          (dataInfo2.attendants && dataInfo2.attendants.filter((e1) => e === e1.demandDate)) ||
          [];
        let activities =
          (dataInfo2.activities && dataInfo2.activities.filter((e1) => e === e1.demandDate)) ||
          [];
        let insurances =
          (dataInfo2.insurances && dataInfo2.insurances.filter((e1) => e === e1.demandDate)) ||
          [];

        let differenceStays =
          (dataInfo2.differences && dataInfo2.differences.filter((e1) => e === e1.differenceDate)) ||
          [];

        schemePlanList.value.push({
          stays: stays,
          places: places,
          caterings: caterings,
          vehicles: vehicles,
          attendants: attendants,
          activities: activities,
          insurances: insurances,
          differenceStays: differenceStays,
          demandDate: e,
          key: Date.now() + i,
        });
        console.log("处理的住宿缓存数据",schemePlanList.value);
        
      });
    }
    
    const dataInfo = props.demandInfo;

    // 🔥 直接处理所有数据，不依赖日期条件
    if ((dataInfo.stays && dataInfo.stays.length > 0) || (dataInfo.places && dataInfo.places.length > 0)) {
      planList.value = [];

      // 收集所有日期
      const allDates = new Set();

      // 从 stays 数据中收集日期
      if (dataInfo.stays) {
        dataInfo.stays.forEach((stay) => {
          allDates.add(stay.demandDate);
        });
      }

      // 从 places 数据中收集日期
      if (dataInfo.places) {
        dataInfo.places.forEach((place) => {
          allDates.add(place.demandDate);
        });
      }

      // 按日期分组所有数据
      const dataByDate = {};
      Array.from(allDates).forEach((date) => {
        dataByDate[date] = {
          stays: dataInfo.stays ? dataInfo.stays.filter((item) => item.demandDate === date) : [],
          places: dataInfo.places ? dataInfo.places.filter((item) => item.demandDate === date) : [],
          caterings: dataInfo.caterings ? dataInfo.caterings.filter((item) => item.demandDate === date) : [],
          vehicles: dataInfo.vehicles ? dataInfo.vehicles.filter((item) => item.demandDate === date) : [],
          attendants: dataInfo.attendants ? dataInfo.attendants.filter((item) => item.demandDate === date) : [],
          activities: dataInfo.activities ? dataInfo.activities.filter((item) => item.demandDate === date) : [],
          insurances: dataInfo.insurances ? dataInfo.insurances.filter((item) => item.demandDate === date) : [],
        };
      });

      // 为每个日期创建 planList 项
      Object.keys(dataByDate).forEach((date, index) => {
        const planItem = {
          stays: dataByDate[date].stays,
          places: dataByDate[date].places,
          caterings: dataByDate[date].caterings,
          vehicles: dataByDate[date].vehicles,
          attendants: dataByDate[date].attendants,
          activities: dataByDate[date].activities,
          insurances: dataByDate[date].insurances,
          demandDate: date,
          key: Date.now() + index,
        };

        planList.value.push(planItem);
      });
    }

    // 原有的日期条件逻辑保持不变，作为备用
    if (dataInfo.startDate && dataInfo.endDate) {
      

      // 日程安排反显
      planList.value = [];

      dateRangeList.value.forEach((e, i) => {
        let stays = (dataInfo.stays && dataInfo.stays.filter((e1) => e === e1.demandDate)) || [];
        let places = (dataInfo.places && dataInfo.places.filter((e1) => e === e1.demandDate)) || [];
        let caterings = (dataInfo.caterings && dataInfo.caterings.filter((e1) => e === e1.demandDate)) || [];
        let vehicles = (dataInfo.vehicles && dataInfo.vehicles.filter((e1) => e === e1.demandDate)) || [];
        let attendants = (dataInfo.attendants && dataInfo.attendants.filter((e1) => e === e1.demandDate)) || [];
        let activities = (dataInfo.activities && dataInfo.activities.filter((e1) => e === e1.demandDate)) || [];
        let insurances = (dataInfo.insurances && dataInfo.insurances.filter((e1) => e === e1.demandDate)) || [];

        // 用餐
        caterings.forEach((e) => {
          e.isInsideHotel = e.isInsideHotel ? 1 : 0;
        });

        // 用车
        vehicles.forEach((e) => {
          if (e.route) {
            // 路线反显
            e.routeList = e.route.split(',');
          }
        });

        // 拓展活动-上传资料-反显
        activities.forEach((j) => {
          if (j.paths && j.paths.length > 0) {
            j.fileList = [];
            j.paths.forEach((g) => {
              let gObj = {};
              let isJson = true;
              try {
                gObj = JSON.parse(g);
              } catch (error) {
                isJson = false;
              }

              if (!isJson) return;

              j.fileList.push({ name: gObj.name, filePath: gObj.url });
            });
          }
        });

        const planItem = {
          stays: stays,
          places: places,
          caterings: caterings,
          vehicles: vehicles,
          attendants: attendants,
          activities: activities,
          insurances: insurances,
          demandDate: e,
          key: Date.now() + i,
        };

        planList.value.push(planItem);
        // console.log("缓存循环",planList.value);
        
      });
    }

    
  },
);

// 时间选择
const changeDate = (startDate: string, endDate: string) => {
  dateRangeList.value = [];

  if (startDate && endDate) {
    // 相差天数
    const returnInt = calculateDateDifference(startDate, endDate);

    for (let index = 0; index <= returnInt; index++) {
      // 日期+n
      dateRangeList.value.push(getNextDay(startDate, index));
    }
  }
};
// 相差天数
const calculateDateDifference = (date1: string, date2: string) => {
  // 将日期转换为Date对象
  const d1 = new Date(date1);
  const d2 = new Date(date2);

  // 计算毫秒差值
  const diffTime = Math.abs(d2.getTime() - d1.getTime());

  // 将毫秒转换为天数
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
};
// 日期加N天
const getNextDay = (date: string, num: number) => {
  // 创建一个新的 Date 对象
  var nextDay = new Date(date);
  // 获取当前日期的天数并加n
  nextDay.setDate(nextDay.getDate() + num);
  // 返回加一天后的日期字符串
  return nextDay.toISOString().split('T')[0];
  // return nextDay.toISOString().split('T')[0].replace(/-/g, '/');
};

// 住宿
const schemeStaysEmit = (staysArr: any) => {
  schemeStays.value[staysArr.schemeIndex] = [...staysArr.schemeStays];
  schemeDifferenceStays.value[staysArr.schemeIndex] = [...staysArr.schemeDifferenceStays];
};

// 会场
const schemePlacesEmit = (placesArr: any) => {
  schemePlaces.value[placesArr.schemeIndex] = [...placesArr.schemePlaces];
};

// 用餐
const schemeCateringsEmit = (cateringsArr: any) => {
  schemeCaterings.value[cateringsArr.schemeIndex] = [...cateringsArr.schemeCaterings];
};

// 用车
const schemeVehiclesEmit = (vehiclesArr: any) => {
  schemeVehicles.value[vehiclesArr.schemeIndex] = [...vehiclesArr.schemeVehicles];
};

// 服务人员
const schemeAttendantsEmit = (attendantsArr: any) => {
  schemeAttendants.value[attendantsArr.schemeIndex] = [...attendantsArr.schemeAttendants];
};

// 拓展活动
const schemeActivityEmit = (activitiesArr: any) => {
  // 对于账单上传，使用过滤后的账单数据
  if (activitiesArr.billActivities) {
    schemeActivities.value[activitiesArr.schemeIndex] = [...activitiesArr.billActivities];
  } else {
    // 兼容其他模式
    schemeActivities.value[activitiesArr.schemeIndex] = [...activitiesArr.schemeActivities];
  }
};

// 保险
const schemeInsurancesEmit = (insurancesArr: any) => {
  schemeInsurances.value[insurancesArr.schemeIndex] = [...insurancesArr.schemeInsurances];
};

// 价格
const schemePriceEmit = (priceObj) => {
  switch (priceObj.type) {
    case 'stay':
      // 住宿
      stayPriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;
      // console.log('%c [ 住宿 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', stayPriceArr.value);
      break;

    case 'place':
      // 会场
      placePriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 会场 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', placePriceArr.value);
      break;

    case 'catering':
      // 用餐
      cateringPriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 用餐 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', cateringPriceArr.value);
      break;

    case 'vehicle':
      // 用车
      vehiclePriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 用车 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', vehiclePriceArr.value);
      break;

    case 'attendant':
      // 服务人员
      attendantPriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 服务 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', attendantPriceArr.value);
      break;

    case 'activity':
      // 拓展活动
      activityPriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 活动 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', activityPriceArr.value);
      break;

    case 'insurance':
      // 保险
      insurancePriceArr.value[priceObj.schemeIndex] = priceObj.totalPrice;

      // console.log('%c [ 保险 - 小计 ]-192', 'font-size:13px; background:pink; color:#bf2c9f;', insurancePriceArr.value);
      break;

    default:
      break;
  }

  const planPrice =
    addSum(stayPriceArr.value) +
    addSum(placePriceArr.value) +
    addSum(cateringPriceArr.value) +
    addSum(vehiclePriceArr.value) +
    addSum(attendantPriceArr.value) +
    addSum(activityPriceArr.value) +
    addSum(insurancePriceArr.value);

  emit('planPriceEmit', planPrice);
  emit('planEachPriceEmit', [
    { type: 1, label: '住宿', price: addSum(stayPriceArr.value) },
    { type: 2, label: '会场', price: addSum(placePriceArr.value) },
    { type: 4, label: '用餐', price: addSum(cateringPriceArr.value) },
    { type: 8, label: '用车', price: addSum(vehiclePriceArr.value) },
    { type: 16, label: '服务人员', price: addSum(attendantPriceArr.value) },
    { type: 32, label: '拓展活动', price: addSum(activityPriceArr.value) },
    { type: 64, label: '保险', price: addSum(insurancePriceArr.value) },
  ]);
};

function addSum(arr) {
  let sum = 0;
  arr.forEach((element) => {
    sum += element;
  });
  return sum;
}

// 字段映射函数 - 将前端数据转换为后端需要的格式
const mapStayDataForBackend = (stayData: any) => {
  return stayData.map((item: any) => ({
    // 保持原有字段
    invoiceTempId: item.invoiceTempId,
    statementTempId: item.statementTempId,
    sourceId: item.sourceId,
    miceDemandHotelId: item.miceDemandHotelId,
    miceDemandStayId: item.miceDemandStayId,
    miceSchemeHotelId: item.miceSchemeHotelId,
    miceSchemeStayId: item.miceSchemeStayId,
    demandDate: item.demandDate,
    roomType: item.roomType,
    breakfastType: item.breakfastType,
    personNum: item.personNum,
    schemeRoomNum: item.schemeRoomNum,
    billRoomNum: item.billRoomNum || item.schemeRoomNum, // 账单房间数，默认等于方案房间数
    discrepancyReason: item.discrepancyReason,
    schemeUnitPrice: item.schemeUnitPrice,
    billUnitPrice: item.billUnitPrice, // 账单单价
    agreementProductId: item.agreementProductId,
    agreementUnitPrice: item.agreementUnitPrice,
    marketUnitPrice: item.marketUnitPrice,
    retailUnitPrice: item.retailUnitPrice,
    msMarketPriceInquiryDetailsId: item.msMarketPriceInquiryDetailsId,
    description: item.description,
  }));
};

// 会场数据映射函数
const mapPlaceDataForBackend = (placeData: any) => {
  return placeData.map((item: any) => ({
    invoiceTempId: item.invoiceTempId,
    statementTempId: item.statementTempId,
    sourceId: item.sourceId,
    miceDemandHotelId: item.miceDemandHotelId,
    miceDemandPlaceId: item.miceDemandPlaceId,
    miceSchemeHotelId: item.miceSchemeHotelId,
    miceSchemePlaceId: item.miceSchemePlaceId,
    demandDate: item.demandDate,
    usageTime: item.usageTime,
    usagePurpose: item.usagePurpose,
    schemePersonNum: item.schemePersonNum,
    billPersonNum: item.billPersonNum || item.schemePersonNum,
    area: item.area,
    underLightFloor: item.underLightFloor,
    tableType: item.tableType,
    hasLed: item.hasLed,
    schemeLedNum: item.schemeLedNum,
    schemeLedSource: item.schemeLedSource,
    schemeGuildhall: item.schemeGuildhall,
    billGuildhall: item.billGuildhall || item.schemeGuildhall,
    billLedNum: item.billLedNum || item.schemeLedNum,
    billLedSource: item.billLedSource || item.schemeLedSource,
    ledSpecs: item.ledSpecs,
    hasTea: item.hasTea,
    teaEachTotalPrice: item.teaEachTotalPrice,
    teaDesc: item.teaDesc,
    schemeUnitPlacePrice: item.schemeUnitPlacePrice,
    billUnitPlacePrice: item.billUnitPlacePrice,
    schemeUnitLedPrice: item.schemeUnitLedPrice,
    billUnitLedPrice: item.billUnitLedPrice,
    schemeUnitTeaPrice: item.schemeUnitTeaPrice,
    billUnitTeaPrice: item.billUnitTeaPrice,
    msMarketPriceInquiryDetailsId: item.msMarketPriceInquiryDetailsId,
    marketPriceUnitPrice: item.marketPriceUnitPrice,
    agreementProductId: item.agreementProductId,
    agreementUnitPrice: item.agreementUnitPrice,
    retailUnitPrice: item.retailUnitPrice,
    description: item.description,
  }));
};

// 用餐数据映射函数
const mapCateringDataForBackend = (cateringData: any) => {
  return cateringData.map((item: any) => ({
    invoiceTempId: item.invoiceTempId,
    statementTempId: item.statementTempId,
    sourceId: item.sourceId,
    isInsideHotel: item.isInsideHotel,
    miceDemandHotelId: item.miceDemandHotelId,
    miceDemandCateringId: item.miceDemandCateringId,
    miceSchemeHotelId: item.miceSchemeHotelId,
    miceSchemeCateringId: item.miceSchemeCateringId,
    demandDate: item.demandDate,
    cateringType: item.cateringType,
    cateringTime: item.cateringTime,
    schemePersonNum: item.schemePersonNum,
    billPersonNum: item.billPersonNum || item.schemePersonNum,
    demandUnitPrice: item.demandUnitPrice,
    isIncludeDrinks: item.isIncludeDrinks,
    schemeUnitPrice: item.schemeUnitPrice,
    billUnitPrice: item.billUnitPrice,
    description: item.description,
  }));
};

// 用车数据映射函数
const mapVehicleDataForBackend = (vehicleData: any) => {
  return vehicleData.map((item: any) => ({
    sourceId: item.sourceId,
    invoiceTempId: item.invoiceTempId,
    statementTempId: item.statementTempId,
    miceDemandVehicleId: item.miceDemandVehicleId,
    miceSchemeVehicleId: item.miceSchemeVehicleId,
    demandDate: item.demandDate,
    usageType: item.usageType,
    usageTime: item.usageTime,
    seats: item.seats,
    schemeVehicleNum: item.schemeVehicleNum,
    billVehicleNum: item.billVehicleNum || item.schemeVehicleNum,
    brand: item.brand,
    route: item.route,
    schemeUnitPrice: item.schemeUnitPrice,
    billUnitPrice: item.billUnitPrice,
    description: item.description,
  }));
};

// 服务人员数据映射函数
const mapAttendantDataForBackend = (attendantData: any) => {
  return attendantData.map((item: any) => ({
    // 必填字段
    invoiceTempId: item.invoiceTempId, // 临时id, 用于关联发票表
    statementTempId: item.statementTempId, // 临时id, 用于关联水单表
    billPersonNum: item.billPersonNum || item.schemePersonNum, // 账单人数
    billUnitPrice: item.billUnitPrice, // 账单单价

    // 可选字段
    sourceId: item.sourceId, // 上一版本id
    miceDemandAttendantId: item.miceDemandAttendantId, // 需求服务人员id
    miceSchemeAttendantId: item.miceDemandAttendantId, // 方案服务人员id
    demandDate: item.demandDate, // 需求日期
    type: item.type, // 人员类型
    schemePersonNum: item.schemePersonNum, // 方案人数
    duty: item.duty, // 工作范围
    schemeUnitPrice: item.schemeUnitPrice, // 方案单价
    description: item.description, // 方案说明
  }));
};

// 每日计划 - 暂存
const schemePlanTempSave = () => {
  // 住宿
  if (stayRef.value) {
    stayRef.value.forEach((e) => {
      e.stayTempSave();
    });
  }

  // 会场
  if (placeRef.value) {
    placeRef.value.forEach((e) => {
      e.placeTempSave();
    });
  }

  // 用餐
  if (cateringRef.value) {
    cateringRef.value.forEach((e) => {
      e.cateringTempSave();
    });
  }

  // 用车
  if (vehicleRef.value) {
    vehicleRef.value.forEach((e) => {
      e.vehicleTempSave();
    });
  }

  // 服务人员
  if (attendantRef.value) {
    attendantRef.value.forEach((e) => {
      e.attendantTempSave();
    });
  }

  // 拓展方案
  if (activityRef.value) {
    activityRef.value.forEach((e) => {
      e.activityTempSave();
    });
  }

  // 保险方案
  if (insuranceRef.value) {
    insuranceRef.value.forEach((e) => {
      e.insuranceTempSave();
    });
  }

  let stays = [];
  let differenceStays = [];
  let places = [];
  let caterings = [];
  let vehicles = [];
  let attendants = [];
  let activities = [];
  let insurances = [];

  schemeStays.value.forEach((e) => {
    stays = stays.concat(e);
  });
  schemeDifferenceStays.value.forEach((e) => {
    differenceStays = differenceStays.concat(e);
  });
  schemePlaces.value.forEach((e) => {
    places = places.concat(e);
  });
  schemeCaterings.value.forEach((e) => {
    caterings = caterings.concat(e);
  });
  schemeVehicles.value.forEach((e) => {
    vehicles = vehicles.concat(e);
  });
  schemeAttendants.value.forEach((e) => {
    attendants = attendants.concat(e);
  });
  schemeActivities.value.forEach((e) => {
    activities = activities.concat(e);
  });
  schemeInsurances.value.forEach((e) => {
    insurances = insurances.concat(e);
  });

  emit('schemePlanEmit', {
    stays: mapStayDataForBackend([...stays]),
    differenceStays: [...differenceStays],
    places: mapPlaceDataForBackend([...places]),
    caterings: mapCateringDataForBackend([...caterings]),
    vehicles: mapVehicleDataForBackend([...vehicles]),
    attendants: mapAttendantDataForBackend([...attendants]),
    activities: [...activities],
    insurances: [...insurances],
  });
};

// 每日计划 - 校验
const SchemePlanSub = () => {
  let verifyStays = true; // 住宿
  let verifyPlaces = true; // 会场
  let verifyCaterings = true; // 用餐
  let verifyVehicles = true; // 用车
  let verifyAttendants = true; // 服务人员
  let verifyActivities = true; // 拓展活动
  let verifyInsurances = true; // 保险
  let verifyTraffic = true; // 交通

  // 住宿
  if (stayRef.value) {
    verifyStays = stayRef.value.every((e) => e.staySub());
  }
  if (!verifyStays) {
    return false;
  }

  // 会场
  if (placeRef.value) {
    verifyPlaces = placeRef.value.every((e) => e.placeSub());
  }
  if (!verifyPlaces) {
    return false;
  }

  // 用餐
  if (cateringRef.value) {
    verifyCaterings = cateringRef.value.every((e) => e.cateringSub());
  }
  if (!verifyCaterings) {
    return false;
  }

  // 用车
  if (vehicleRef.value) {
    verifyVehicles = vehicleRef.value.every((e) => e.vehicleSub());
  }
  if (!verifyVehicles) {
    return false;
  }

  // 服务人员
  if (attendantRef.value) {
    verifyAttendants = attendantRef.value.every((e) => e.attendantSub());
  }
  if (!verifyAttendants) {
    return false;
  }

  // 拓展方案
  if (activityRef.value) {
    verifyActivities = activityRef.value.every((e) => e.activitySub());
  }
  if (!verifyActivities) {
    return false;
  }

  // 保险方案
  if (insuranceRef.value) {
    verifyInsurances = insuranceRef.value.every((e) => e.insuranceSub());
  }
  if (!verifyInsurances) {
    return false;
  }

  // 是否校验通过
  const isAllVer =
    verifyStays &&
    verifyPlaces &&
    verifyCaterings &&
    verifyVehicles &&
    verifyAttendants &&
    verifyActivities &&
    verifyInsurances;
  // TODO - 交通
  // && verifyTraffic

  if (isAllVer) {
    let stays = [];
    let differenceStays = [];
    let places = [];
    let caterings = [];
    let vehicles = [];
    let attendants = [];
    let activities = [];
    let insurances = [];

    schemeStays.value.forEach((e) => {
      stays = stays.concat(e);
    });
    schemeDifferenceStays.value.forEach((e) => {
      differenceStays = differenceStays.concat(e);
    });
    schemePlaces.value.forEach((e) => {
      places = places.concat(e);
    });
    schemeCaterings.value.forEach((e) => {
      caterings = caterings.concat(e);
    });
    schemeVehicles.value.forEach((e) => {
      vehicles = vehicles.concat(e);
    });
    schemeAttendants.value.forEach((e) => {
      attendants = attendants.concat(e);
    });
    schemeActivities.value.forEach((e) => {
      activities = activities.concat(e);
    });
    schemeInsurances.value.forEach((e) => {
      insurances = insurances.concat(e);
    });

    emit('schemePlanEmit', {
      stays: mapStayDataForBackend([...stays]),
      differenceStays: [...differenceStays],
      places: mapPlaceDataForBackend([...places]),
      caterings: mapCateringDataForBackend([...caterings]),
      vehicles: mapVehicleDataForBackend([...vehicles]),
      attendants: mapAttendantDataForBackend([...attendants]),
      activities: [...activities],
      insurances: [...insurances],
    });
  }

  return isAllVer;
};

// 🔥 新增：调试用餐组件的方法
const debugCateringComponents = () => {
  console.log('%c [ 调试用餐组件 ]', 'font-size:14px; background:purple; color:#fff;');
  if (cateringRef.value) {
    cateringRef.value.forEach((ref, index) => {
      console.log(`用餐组件${index}:`, ref);
      if (ref.debugSchemeUnitPrice) {
        ref.debugSchemeUnitPrice();
      }
      if (ref.forceRecalculatePrice) {
        ref.forceRecalculatePrice();
      }
    });
  }
};

defineExpose({ SchemePlanSub, schemePlanTempSave, insuranceRef, debugCateringComponents });

onMounted(async () => {
  console.log(props.merchantType);

  // 初始化滚动检测
  initScrollDetection();
});

onBeforeUnmount(() => {
  // 清理交叉观察器
  if (intersectionObserver) {
    intersectionObserver.disconnect();
    intersectionObserver = null;
  }
});

// 初始化滚动检测
const initScrollDetection = () => {
  if (!planComponentRef.value) return;

  // 获取滚动容器元素
  const scrollContainer = typeof props.schemeContainerRef === 'function'
    ? props.schemeContainerRef()
    : props.schemeContainerRef;

  // 创建交叉观察器
  intersectionObserver = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        // 当组件完全离开视口时，隐藏固定日期标题
        showDateAffix.value = entry.isIntersecting || entry.intersectionRatio > 0;
      });
    },
    {
      // 设置根元素为滚动容器
      root: scrollContainer as Element,
      // 设置阈值，当组件完全离开视口时触发
      threshold: 0,
      // 设置根边距，提前一点触发
      rootMargin: '-20px 0px 0px 0px'
    }
  );

  // 开始观察组件
  intersectionObserver.observe(planComponentRef.value);
};
</script>

<template>
  <!-- 方案互动-日程安排 -->
  <div ref="planComponentRef" class="interact_schedule_plan">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>日程安排</span>
    </div>

    <div v-for="(item, idx) in planList" :key="item.demandDate">
      <a-affix
        v-show="showDateAffix"
        :offset-top="20"
        :target="() => props.schemeContainerRef"
      >
        <div class="date_title mt20 mb20">{{ item.demandDate }}</div>
      </a-affix>
      <div
        v-if="
          (['/bidman/bill/confirm'].includes(route.path) && [1, 2].includes(merchantTypeCurrent)) ||
          !['/bidman/bill/confirm'].includes(route.path)
        "
      >
        <!-- 住宿 -->
        <scheme-stay
          v-if="item.stays && item.stays.length > 0 && (merchantTypeCurrent === 1 || merchantTypeCurrent === 2)"
          ref="stayRef"
          class="interact_stay"
          :showBindingScheme="showBindingScheme"
          :processNode="props.processNode"
          :schemeType="props.schemeType"
          :schemeItem="item"
          :schemeIndex="idx"
          :isSchemeCache="props.isSchemeCache"
          :schemeCacheItem="schemePlanList[idx]"
          :demandHotels="props.demandInfo.hotels"
          :hotels="props.hotelList"
          :merchantType="merchantTypeCurrent"
          :readonly="props.readonly"
          @schemePriceEmit="schemePriceEmit"
          @schemeStaysEmit="schemeStaysEmit"
        />
        <!-- 会场 -->
        <scheme-place
          v-if="item.places && item.places.length > 0 && (merchantTypeCurrent === 1 || merchantTypeCurrent === 2)"
          ref="placeRef"
          class="interact_place"
          :showBindingScheme="showBindingScheme"
          :schemeType="props.schemeType"
          :schemeItem="item"
          :schemeIndex="idx"
          :isSchemeCache="props.isSchemeCache"
          :schemeCacheItem="schemePlanList[idx]"
          :demandHotels="props.demandInfo.hotels"
          :hotels="props.hotelList"
          :merchantType="merchantTypeCurrent"
          :readonly="props.readonly"
          @schemePriceEmit="schemePriceEmit"
          @schemePlacesEmit="schemePlacesEmit"
        />
        <!-- 用餐 -->
        <scheme-catering
          v-if="item.caterings && item.caterings.length > 0 && (merchantTypeCurrent === 1 || merchantTypeCurrent === 2)"
          ref="cateringRef"
          class="interact_can"
          :showBindingScheme="showBindingScheme"
          :schemeType="props.schemeType"
          :schemeItem="item"
          :schemeIndex="idx"
          :isSchemeCache="props.isSchemeCache"
          :schemeCacheItem="schemePlanList[idx]"
          :demandHotels="props.demandInfo.hotels"
          :hotels="props.hotelList"
          :merchantType="merchantTypeCurrent"
          :isCateringStandardControl="props.isCateringStandardControl"
          :readonly="props.readonly"
          @schemePriceEmit="schemePriceEmit"
          @schemeCateringsEmit="schemeCateringsEmit"
        />
        <!-- 用车 -->
        <scheme-vehicle
          v-if="
            item.vehicles &&
            item.vehicles.length > 0 &&
            (merchantTypeCurrent === 1 || merchantTypeCurrent === 2 || merchantTypeCurrent === 5)
          "
          ref="vehicleRef"
          class="interact_che"
          :showBindingScheme="showBindingScheme"
          :schemeType="props.schemeType"
          :schemeItem="item"
          :demandInfo="props.demandInfo"
          :schemeIndex="idx"
          :isSchemeCache="props.isSchemeCache"
          :schemeCacheItem="schemePlanList[idx]"
          :readonly="props.readonly"
          @schemePriceEmit="schemePriceEmit"
          @schemeVehiclesEmit="schemeVehiclesEmit"
        />
        <!-- 服务人员 -->
        <scheme-attendant
          v-if="
            item.attendants && item.attendants.length > 0 && (merchantTypeCurrent === 1 || merchantTypeCurrent === 2)
          "
          ref="attendantRef"
          class="interact_service"
          :showBindingScheme="showBindingScheme"
          :schemeType="props.schemeType"
          :schemeItem="item"
          :schemeIndex="idx"
          :isSchemeCache="props.isSchemeCache"
          :schemeCacheItem="schemePlanList[idx]"
          :readonly="props.readonly"
          @schemePriceEmit="schemePriceEmit"
          @schemeAttendantsEmit="schemeAttendantsEmit"
        />
        <!-- 拓展活动 -->
        <scheme-activity
          v-if="
            item.activities && item.activities.length > 0 && (merchantTypeCurrent === 1 || merchantTypeCurrent === 2)
          "
          ref="activityRef"
          class="interact_activity"
          :showBindingScheme="showBindingScheme"
          :schemeType="props.schemeType"
          :schemeItem="item"
          :schemeIndex="idx"
          :isSchemeCache="props.isSchemeCache"
          :schemeCacheItem="schemePlanList[idx]"
          :readonly="props.readonly"
          @schemePriceEmit="schemePriceEmit"
          @schemeActivityEmit="schemeActivityEmit"
        />
      </div>
      <div
        v-if="
          (['/bidman/bill/confirm'].includes(route.path) && [3].includes(merchantTypeCurrent)) ||
          !['/bidman/bill/confirm'].includes(route.path)
        "
      >
        <!-- 保险 -->
        <scheme-insurance
          v-if="item.insurances && item.insurances.length > 0 && merchantTypeCurrent === 3"
          ref="insuranceRef"
          class="interact_ins"
          :showBindingScheme="showBindingScheme"
          :schemeType="props.schemeType"
          :schemeItem="item"
          :schemeIndex="idx"
          :isSchemeCache="props.isSchemeCache"
          :schemeCacheItem="schemePlanList[idx]"
          :readonly="props.readonly"
          @schemePriceEmit="schemePriceEmit"
          @schemeInsurancesEmit="schemeInsurancesEmit"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.interact_schedule_plan {
  .date_title {
    display: inline-block;
    padding: 4px 16px;

    font-size: 14px;
    color: #ffffff;

    background: #1868db;
    border-radius: 4px;
  }

  .interact_stay {
  }
  .interact_place {
  }
  .interact_can {
  }
  .interact_che {
  }
  .interact_service {
  }
  .interact_activity {
  }
  .interact_ins {
  }
}
</style>
