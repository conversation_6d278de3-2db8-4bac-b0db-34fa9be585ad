<script setup lang="ts">
// 方案互动-布展物料
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';
import { MaterialTypeConstant } from '@haierbusiness-front/common-libs';

const props = defineProps({
  demandInfo: {
    type: Object,
    default: {},
  },
  schemeCacheInfo: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
  showBindingScheme: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(['materialPriceEmit', 'schemeMaterialEmit']);

const oldSchemeList = ref<Array<any>>([]);
const newSchemeList = ref<Array<any>>([]);

const subtotal = ref<number>(0); // 小计

watch(
  () => [props.demandInfo, props.schemeCacheInfo],
  () => {

    if (props.demandInfo.material && props.demandInfo.material.materialDetails) {
      oldSchemeList.value = JSON.parse(JSON.stringify(props.demandInfo))?.material?.materialDetails || [];

      if (props.isSchemeCache && props.schemeCacheInfo) {
        // 缓存 - 反显，但需要确保sourceId正确
        const cacheData = props.schemeCacheInfo?.material?.materialDetails || [];
        const demandData = props.demandInfo?.material?.materialDetails || [];

        newSchemeList.value = cacheData.map((cacheItem) => {
          // 🔧 从原始详情数据中查找对应的sourceId
          // 注意：这里应该通过 miceDemandMaterialDetailsId 匹配 demandData 中的 id
          const matchingDemandItem = demandData.find(
            (demandItem) => demandItem.id === cacheItem.miceDemandMaterialDetailsId,
          );

          // 🔧 优先使用匹配到的原始数据的sourceId，如果没有则使用缓存中的sourceId
          const correctSourceId = matchingDemandItem?.sourceId || cacheItem.sourceId;

  
          return {
            ...cacheItem,
            sourceId: correctSourceId, // 🔧 确保使用正确的sourceId
            // 🔧 确保账单字段存在，如果没有则使用方案字段作为默认值
            billMaterialNum: cacheItem.billMaterialNum || cacheItem.schemeMaterialNum,
            billUnitPrice: cacheItem.billUnitPrice || cacheItem.schemeUnitPrice,
          };
        });
      } else {
        const demandData = JSON.parse(JSON.stringify(props.demandInfo))?.material?.materialDetails || [];
        newSchemeList.value = demandData.map((e) => {
          // 🔧 每个 materialDetails 项目都有自己的 sourceId
          // 这个 sourceId 应该是该项目在原始需求中的唯一标识
          const itemSourceId = e.sourceId || e.id; // 优先使用sourceId，兜底使用id

  
          return {
            miceSchemeMaterialId: props.demandInfo.material.id,
            miceDemandMaterialDetailsId: e.id,
            sourceId: itemSourceId, // 🔧 使用该项目自己的sourceId

            type: e.type,
            specs: e.specs,
            schemeMaterialNum: e.num,
            unit: e.unit || '元/人', // 添加单位字段，默认为元/人

            demandUnitPrice: e.unitPrice,
            schemeUnitPrice: e.unitPrice,

            // 🔧 添加账单字段，默认使用方案字段的值
            billMaterialNum: e.num,
            billUnitPrice: e.unitPrice,
          };
        });
      }

      // 小计 - 🔧 使用账单字段计算
      subtotal.value = 0;
      newSchemeList.value.forEach((e) => {
        // 🔧 优先使用账单字段，兜底使用方案字段
        const unitPrice = e.billUnitPrice || e.schemeUnitPrice;
        const materialNum = e.billMaterialNum || e.schemeMaterialNum;

        if (unitPrice && materialNum) {
          subtotal.value += unitPrice * materialNum;
        }
      });

      emit('materialPriceEmit', subtotal.value);
    }
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['物料类型', '规格说明', '数量', '单价', '单位'];

// 🔧 处理物料数量和单价变化
const handleMaterialChange = () => {
  // 重新计算小计 - 🔧 使用账单字段计算
  subtotal.value = 0;
  newSchemeList.value.forEach((item) => {
    // 🔧 优先使用账单字段，兜底使用方案字段
    const unitPrice = item.billUnitPrice || item.schemeUnitPrice;
    const materialNum = item.billMaterialNum || item.schemeMaterialNum;

    if (unitPrice && materialNum) {
      subtotal.value += unitPrice * materialNum;
    }
  });

  // 通知父组件价格变化
  emit('materialPriceEmit', subtotal.value);

  // 自动暂存数据
  materialTempSave();
};

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice =
      newSchemeList.value[index].biddingPrice * newSchemeList.value[index].schemeMaterialNum;
  }

  const isAllPriceWrite = newSchemeList.value.every((e) => e.planPrice && e.planPrice > 0);
  subtotal.value = 0;

  if (isAllPriceWrite) {
    newSchemeList.value.forEach((e) => {
      subtotal.value += e.planPrice;
    });

    emit('materialPriceEmit', subtotal.value);
  }
};

// 暂存
const materialTempSave = () => {
  // 🔧 获取主 material 的 sourceId（用于主对象）
  let materialSourceId = null;
  if (props.isSchemeCache && props.schemeCacheInfo?.material?.sourceId) {
    // 如果有缓存数据，优先使用缓存中的sourceId
    materialSourceId = props.schemeCacheInfo.material.sourceId;
  } else if (props.demandInfo?.material?.sourceId) {
    // 使用接口返回的sourceId字段
    materialSourceId = props.demandInfo.material.sourceId;
  } else {
    // 兜底：使用当前物料的id
    materialSourceId = props.demandInfo.material.id;
  }
  // 🔧 确保 materialDetails 中每个项目的 miceSchemeMaterialId 使用 miceSchemeMaterialDetailsId
  const materialDetailsWithCorrectId = newSchemeList.value.map((item) => ({
    ...item,
    miceSchemeMaterialId: item.miceSchemeMaterialId, // 🔧 使用 materialDetails 中的 miceSchemeMaterialDetailsId
  }));

  emit('schemeMaterialEmit', {
    schemeMaterial: {
      invoiceTempId: null, // 临时id, 用于关联发票表
      statementTempId: null, // 临时id, 用于关联水单表
      sourceId: materialSourceId, // 🔧 主 material 的 sourceId
      miceDemandMaterialId: props.demandInfo.material.id,
      miceSchemeMaterialId: props.demandInfo.material.id,
      demandTotalPrice: props.demandInfo.material.demandTotalPrice,
      schemeTotalPrice: props.demandInfo.material.demandTotalPrice,
      billTotalPrice: subtotal.value, // 🔧 账单总价 - 使用计算出的小计
      description: null, // 方案说明
      materialDetails: materialDetailsWithCorrectId, // 🔧 使用修正后的 materialDetails
    },
  });
};

// 校验
const materialSub = () => {
  let isVerPassed = true;

  // newSchemeList.value.forEach((e, i) => {
  //   if (!e.schemeUnitPrice) {
  //     message.error('请输入' + e.demandDate + '布展物料' + (i + 1) + '竞价单价');

  //     isVerPassed = false;
  //     return;
  //   }
  // });

  if (isVerPassed) {
    materialTempSave();
  }

  return isVerPassed;
};

defineExpose({ materialSub, materialTempSave });

onMounted(async () => {});
</script>

<template>
  <!-- 布展物料 -->
  <div class="scheme_vehicle">
    <div class="interact_title">
      <div class="interact_shu mr20"></div>
      <span>布展物料</span>
    </div>

    <div class="common_table mt16">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_table" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '布展物料' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
                </template>
                {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeMaterialNum || '-' }}
                </template>
                {{ item.schemeMaterialNum || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.schemeUnitPrice || '-' }}
                </template>
                {{ item.schemeUnitPrice || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '元/人' }}
                </template>
                {{ item.unit || '元/人' }}
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_table" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '布展物料' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
                </template>
                {{ MaterialTypeConstant.ofType(item.type)?.desc || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.specs || '-' }}
                </template>
                {{ item.specs || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value">
              <!-- 🔧 数量输入框 - 使用 billMaterialNum 字段 -->
              <div>
                <a-input-number
                  v-model:value="item.billMaterialNum"
                  :min="0"
                  :precision="0"
                  :max="9999"
                  placeholder=""
                  :bordered="false"
                  allow-clear
                  style="width: calc(100% - 30px)"
                  @change="handleMaterialChange"
                />
                <div class="scheme_plan_edit"></div>
              </div>
            </div>
            <div class="scheme_plan_value ">
              <!-- 🔧 单价输入框 - 使用 billUnitPrice 字段 -->
              <div>
                <a-input-number
                  v-model:value="item.billUnitPrice"
                  :min="0"
                  :precision="2"
                  :max="999999"
                  placeholder=""
                  :bordered="false"
                  allow-clear
                  style="width: calc(100% - 30px)"
                  @change="handleMaterialChange"
                />
                <div class="scheme_plan_edit"></div>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.unit || '元/人' }}
                </template>
                {{ item.unit || '元/人' }}
              </a-tooltip>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                {{
                  // 🔧 优先使用账单字段，兜底使用方案字段
                  (item.billUnitPrice || item.schemeUnitPrice) && (item.billMaterialNum || item.schemeMaterialNum)
                    ? '¥' +
                      formatNumberThousands(
                        (item.billUnitPrice || item.schemeUnitPrice) * (item.billMaterialNum || item.schemeMaterialNum),
                      )
                    : '-'
                }}
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <div
                v-if="(item.billUnitPrice || item.schemeUnitPrice) && (item.billMaterialNum || item.schemeMaterialNum)"
              >
                {{
                  (item.billMaterialNum || item.schemeMaterialNum) +
                  '个*' +
                  (item.billUnitPrice || item.schemeUnitPrice) +
                  '(' +
                  (item.unit || '元/人') +
                  ')'
                }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_material.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  // 🔧 完全模仿住宿组件的样式

  .scheme_plan_value {
    /* 数量输入框 - 模仿住宿组件的房间数输入框 */
    :deep(.ant-input-number .ant-input-number-input) {
      height: 22px;
      padding: 0 px;
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  .scheme_plan_price_value {
    /* 单价输入框 - 模仿住宿组件的竞价单价 */
    :deep(.ant-input-number .ant-input-number-input) {
      /* input数字输入框 */
      height: 24px;
      padding: 0 5px;
      text-align: end;

      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      border-bottom: 1px solid #4e5969;
    }

    /* 只读模式样式 */
    &.readonly {
      width: 84px;
      font-weight: 500;
      font-size: 14px;
      color: #1868db;
      text-align: right;
      height: 24px;
      line-height: 24px;
      padding: 0 5px;
    }
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
