<script setup lang="ts">
// 会议厅选择
import { defineEmits, defineProps, onMounted, ref, reactive } from 'vue';

const props = defineProps({
  guildhall: {
    type: String,
    default: '',
  },
  guildhallPhotos: {
    type: Array,
    default: [],
  },
});

const emit = defineEmits(['demandBaseFunc']);

const schemeGuildhallFormRef = ref();

const formState = reactive<any>({
  miceName: '',
});

// 校验
const rules = {
  miceName: [
    { required: true, message: '请填写会议名称', trigger: 'change' },
    { min: 1, max: 50, message: '长度不超过50个字符', trigger: 'blur' },
  ],
};

// 提交
const onSubmit = () => {
  schemeGuildhallFormRef.value.validate().then(() => {
    emit('demandBaseFunc', {});
  });
};

onMounted(async () => {});
</script>

<template>
  <!-- 会议厅选择 -->
  <div class="demand_base">
    <a-form
      class="mt24"
      ref="schemeGuildhallFormRef"
      :model="formState"
      :rules="rules"
      layout="vertical"
      hideRequiredMark
    >
      <a-row :gutter="12">
        <a-col :span="6">
          <a-form-item label="会议名称：" name="miceName">
            <a-input v-model:value="formState.miceName" placeholder="请填写会议名称" :maxlength="50" allow-clear />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>
  </div>
</template>

<style scoped lang="less">
.scheme_guildhall {
}
</style>
